"""Comprehensive Gradio Interface for HVAC Multi-Agent System."""

import asyncio
import json
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import base64
from io import BytesIO

import gradio as gr
from loguru import logger

# Import system components
from .main import HVACEmailAnalysisSystem
from .krabulon.agents import AgentOrchestrator
from .krabulon.agents.quote_analyzer import QuoteAnalyzerAgent
from .krabulon.agents.equipment_matcher import EquipmentMatcherAgent
from .krabulon.agents.quote_generator import QuoteGeneratorAgent
from .database.sql.models import db_manager


class HVACGradioInterface:
    """Comprehensive Gradio interface for HVAC systems."""
    
    def __init__(self):
        self.hvac_system = HVACEmailAnalysisSystem()
        self.krabulon_orchestrator = None
        self.quote_analyzer = QuoteAnalyzerAgent()
        self.equipment_matcher = EquipmentMatcherAgent()
        self.quote_generator = QuoteGeneratorAgent()
        
        # Initialize system
        self._initialize_system()
    
    def _initialize_system(self):
        """Initialize the HVAC system components."""
        try:
            # Initialize databases
            self.hvac_system.initialize_databases()
            
            # Initialize Krabulon orchestrator
            asyncio.run(self._init_krabulon())
            
            logger.info("HVAC Gradio Interface initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize HVAC system: {e}")
    
    async def _init_krabulon(self):
        """Initialize Krabulon orchestrator."""
        try:
            self.krabulon_orchestrator = AgentOrchestrator()
            await self.krabulon_orchestrator.initialize()
        except Exception as e:
            logger.error(f"Failed to initialize Krabulon: {e}")
    
    def create_interface(self) -> gr.Blocks:
        """Create the main Gradio interface."""
        
                with gr.Blocks(
                    title="HVAC Multi-Agent System",
                    theme=grSohs.),Soft(),
            css"
            .gradio-  .trinca {{
                max-wid h 1200px !imprant;
            }
            .t b- dv {ear-gradient(90deg, #1f4e79, #2d5aa0);
                backg ound: line r-gra  nt(90g, #14e79 #2d5aa0);
            }
            .s ktos-sccss {
                 dlor: #c3-cclo: #4dda;
                  cd1r-24;: #c36cb;
                : #155724;
                 ddding: 10px;
                radius-;dis: 5px;
            }
            .s a u- {
                 r-clo: #f87d;
                  od-r-or: #: #f5c6cb;
                or6cb: #721c24;
                p:2ig: 10px;
                  u5r-ius: 5px;
            }
            "
        ) s itfa:
            
            # Hee
            gr.Mkow("
            # 🏠 HVAC M l -Aget Syte
            ### Kompleksowy sys em araMizy emaili, generowania ofdrt i zawządzanin (anym" HVAC
            "An) System
            tem analizy emaili, generowania ofert i zarządzania danymi HVAC
            # S a us i"dictr
            wi h g.R():
                sys emSttttui t gr.HTML(
                    val e=self.tr.tRsyst:mstaus_tl()
                    lymalr.Status syHteTuM
                )(
                refresh_ tn = gr.B     ("🔄 Oeświ=ż ltafus", sizget_me)
            _status_html(),
            # Main   ay
            wi h .Tb():
                
                # Ta  1: Ereif An lysrB"🔄 Odśwież status", size="sm")
                h gr.Tab(📧 Aaliza Eii):
                    sehf._cg.aab)emil_alyis_tab()
                
                # Ta  2: Q   1 Gane Analy
                wi h gr.Tib("📋 Grn( Aar Ofmrta):li"):
                    se f._c  ael_quo.e_amal_ation_tab()alysis_tab()
                
                # Ta  3: Eq ipn Dabas
                wi h T .Tab(e🔧 Baza Sprzęaui):n
                     etf. crraTeaequepmenr_databasa_tab()r Ofert"):
                tion_tab()
                # Ta  4: Krab l   Dt Eichmt
                with gr.Ta ("🌐 Krab l   - Wz og3:apiD Danych):
                     etf. crerte_Tbabul( tzb()
                
                # Ta  5: Sys ew Minun-m Wtogacanie Danych"):
                wi h gr.T("⚙️ Zrzązanie Syteme):
          gt         ehf. cre.te_sya(em_ma️agement_t b()emem"):
                m_management_tab()
                # Ta  6: A yti & Reps
                with gr.Ta ("📊 A ihi.yka a Rap rtya):ityka i Raporty"):
         lc(        self._crea e_ayti_ab()
         
            # Eve d hrs
            refresh_  e.c_c(
                f =self. refnsshesy.tfm_stt
        button_small_disabled="background-color: gray; cursor: not-allowed",
            )
        
        return interface
    
    def _create_email_analysis_tab(self):
        """Create email analysis tab."""
        
        gr.Markdown("### Analiza emaili HVAC z wykorzystaniem AI")
        
        with gr.Row():
            with gr.Column(scale=1):
                # Email input
                email_input = gr.Textbox(
                    label="Treść emaila",
                    placeholder="Wklej tutaj treść emaila od klienta...",
                    lines=10
                )
                
                # Framework selection
                framework_choice = gr.Radio(
                    choices=["LangGraph", "CrewAI", "OpenAI Swarm"],
                    value="LangGraph",
                    label="Framework AI"
                )
                
                # Analysis options
                with gr.Accordion("Opcje analizy", open=False):
                    include_sentiment = gr.Checkbox(
                        label="Analiza sentymentu",
                        value=True
                    )
                    include_entities = gr.Checkbox(
                        label="Rozpoznawanie encji",
                        value=True
                    )
                    include_intent = gr.Checkbox(
                        label="Klasyfikacja intencji",
                        value=True
                    )
                
                analyze_btn = gr.Button("🔍 Analizuj Email", variant="primary")
            
            with gr.Column(scale=1):
                # Results
                analysis_results = gr.JSON(
                    label="Wyniki analizy",
                    value={}
                )
                
                # Customer insights
                customer_insights = gr.Textbox(
                    label="Wnioski o kliencie",
                    lines=5,
                    interactive=False
                )
                
                # Recommended actions
                recommended_actions = gr.Textbox(
                    label="Rekomendowane działania",
                    lines=3,
                    interactive=False
                )
        
        # Event handler
        analyze_btn.click(
            fn=self._analyze_email,
            inputs=[email_input, framework_choice, include_sentiment, include_entities, include_intent],
            outputs=[analysis_results, customer_insights, recommended_actions]
        )
    
    def _create_quote_generation_tab(self):
        """Create quote generation tab."""
        
        gr.Markdown("### Generator profesjonalnych ofert HVAC")
        
        with gr.Row():
            with gr.Column(scale=1):
                # Customer information
                gr.Markdown("#### Informacje o kliencie")
                customer_name = gr.Textbox(label="Imię i nazwisko")
                customer_company = gr.Textbox(label="Firma (opcjonalnie)")
                customer_email = gr.Textbox(label="Email")
                customer_phone = gr.Textbox(label="Telefon")
                customer_address = gr.Textbox(label="Adres", lines=2)
                
                # Project requirements
                gr.Markdown("#### Wymagania projektu")
                room_count = gr.Number(label="Liczba pomieszczeń", value=1)
                total_area = gr.Number(label="Powierzchnia całkowita (m²)", value=50)
                cooling_priority = gr.Radio(
                    choices=["Wysokie", "Średnie", "Niskie"],
                    value="Średnie",
                    label="Priorytet chłodzenia"
                )
                heating_priority = gr.Radio(
                    choices=["Wysokie", "Średnie", "Niskie"],
                    value="Średnie",
                    label="Priorytet ogrzewania"
                )
                budget_range = gr.Radio(
                    choices=["Premium", "Standard", "Budżetowe"],
                    value="Standard",
                    label="Zakres budżetowy"
                )
                
                # Additional requirements
                with gr.Accordion("Dodatkowe wymagania", open=False):
                    wifi_required = gr.Checkbox(label="Sterowanie WiFi")
                    quiet_operation = gr.Checkbox(label="Cicha praca")
                    air_purification = gr.Checkbox(label="Oczyszczanie powietrza")
                    smart_features = gr.Checkbox(label="Funkcje smart")
                
                generate_quote_btn = gr.Button("📋 Generuj Ofertę", variant="primary")
            
            with gr.Column(scale=1):
                # Quote preview
                quote_preview = gr.HTML(
                    label="Podgląd oferty",
                    value="<p>Oferta zostanie wygenerowana po wypełnieniu formularza.</p>"
                )
                
                # Download options
                with gr.Row():
                    download_pdf = gr.File(
                        label="Pobierz PDF",
                        visible=False
                    )
                    download_txt = gr.File(
                        label="Pobierz TXT",
                        visible=False
                    )
        
        # Event handler
        generate_quote_btn.click(
            fn=self._generate_quote,
            inputs=[
                customer_name, customer_company, customer_email, customer_phone, customer_address,
                room_count, total_area, cooling_priority, heating_priority, budget_range,
                wifi_required, quiet_operation, air_purification, smart_features
            ],
            outputs=[quote_preview, download_pdf, download_txt]
        )
    
    def _create_equipment_database_tab(self):
        """Create equipment database tab."""
        
        gr.Markdown("### Baza danych sprzętu HVAC")
        
        with gr.Row():
            with gr.Column(scale=1):
                # Search and filters
                search_term = gr.Textbox(
                    label="Wyszukaj sprzęt",
                    placeholder="Wpisz model, producenta lub typ..."
                )
                
                manufacturer_filter = gr.Dropdown(
                    choices=["Wszystkie", "LG", "Daikin", "Mitsubishi", "Samsung"],
                    value="Wszystkie",
                    label="Producent"
                )
                
                equipment_type_filter = gr.Dropdown(
                    choices=["Wszystkie", "Split", "Multi-split", "VRF", "Kanałowe"],
                    value="Wszystkie",
                    label="Typ sprzętu"
                )
                
                search_btn = gr.Button("🔍 Szukaj", variant="primary")
                
                # Add new equipment
                with gr.Accordion("Dodaj nowy sprzęt", open=False):
                    new_manufacturer = gr.Textbox(label="Producent")
                    new_model = gr.Textbox(label="Model")
                    new_type = gr.Dropdown(
                        choices=["split_system", "multi_split", "vrf", "ducted"],
                        label="Typ"
                    )
                    new_cooling_capacity = gr.Number(label="Moc chłodnicza (kW)")
                    new_price = gr.Number(label="Cena (PLN)")
                    
                    add_equipment_btn = gr.Button("➕ Dodaj sprzęt")
            
            with gr.Column(scale=2):
                # Equipment list
                equipment_list = gr.Dataframe(
                    headers=["Producent", "Model", "Typ", "Moc (kW)", "Cena (PLN)", "Klasa energetyczna"],
                    datatype=["str", "str", "str", "number", "number", "str"],
                    label="Lista sprzętu",
                    interactive=False
                )
                
                # Equipment details
                equipment_details = gr.JSON(
                    label="Szczegóły sprzętu",
                    value={}
                )
        
        # Event handlers
        search_btn.click(
            fn=self._search_equipment,
            inputs=[search_term, manufacturer_filter, equipment_type_filter],
            outputs=[equipment_list]
        )
        
        add_equipment_btn.click(
            fn=self._add_equipment,
            inputs=[new_manufacturer, new_model, new_type, new_cooling_capacity, new_price],
            outputs=[equipment_list]
        )
    
    def _create_krabulon_tab(self):
        """Create Krabulon data enrichment tab."""
        
        gr.Markdown("### Krabulon - Automatyczne wzbogacanie bazy danych sprzętu")
        
        with gr.Row():
            with gr.Column(scale=1):
                # Manufacturer selection
                manufacturers_input = gr.CheckboxGroup(
                    choices=["LG", "Daikin", "Mitsubishi", "Samsung", "Panasonic", "Fujitsu"],
                    value=["LG", "Daikin"],
                    label="Wybierz producentów"
                )
                
                # Crawling options
                with gr.Accordion("Opcje crawlingu", open=False):
                    max_pages = gr.Slider(
                        minimum=10,
                        maximum=100,
                        value=50,
                        step=10,
                        label="Maksymalna liczba stron"
                    )
                    
                    crawl_delay = gr.Slider(
                        minimum=0.5,
                        maximum=5.0,
                        value=1.0,
                        step=0.5,
                        label="Opóźnienie między żądaniami (s)"
                    )
                
                start_enrichment_btn = gr.Button("🌐 Rozpocznij wzbogacanie", variant="primary")
                
                # Progress
                enrichment_progress = gr.Progress()
                
            with gr.Column(scale=1):
                # Status and results
                enrichment_status = gr.Textbox(
                    label="Status procesu",
                    value="Gotowy do rozpoczęcia",
                    interactive=False
                )
                
                enrichment_results = gr.JSON(
                    label="Wyniki wzbogacania",
                    value={}
                )
                
                # Statistics
                enrichment_stats = gr.HTML(
                    label="Statystyki",
                    value="<p>Brak danych</p>"
                )
        
        # Event handler
        start_enrichment_btn.click(
            fn=self._start_krabulon_enrichment,
            inputs=[manufacturers_input, max_pages, crawl_delay],
            outputs=[enrichment_status, enrichment_results, enrichment_stats]
        )
    
    def _create_system_management_tab(self):
        """Create system management tab."""
        
        gr.Markdown("### Zarządzanie systemem")
        
        with gr.Row():
            with gr.Column():
                # Database management
                gr.Markdown("#### Zarządzanie bazami danych")
                
                with gr.Row():
                    init_db_btn = gr.Button("🗄️ Inicjalizuj bazy danych")
                    backup_db_btn = gr.Button("💾 Backup baz danych")
                    restore_db_btn = gr.Button("🔄 Przywróć backup")
                
                # System health
                gr.Markdown("#### Zdrowie systemu")
                
                health_check_btn = gr.Button("🏥 Sprawdź zdrowie systemu")
                health_results = gr.JSON(label="Wyniki sprawdzenia")
                
                # Logs
                gr.Markdown("#### Logi systemu")
                
                log_level = gr.Dropdown(
                    choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                    value="INFO",
                    label="Poziom logów"
                )
                
                show_logs_btn = gr.Button("📋 Pokaż logi")
                logs_display = gr.Textbox(
                    label="Logi",
                    lines=10,
                    interactive=False
                )
        
        # Event handlers
        init_db_btn.click(fn=self._initialize_databases)
        health_check_btn.click(
            fn=self._check_system_health,
            outputs=[health_results]
        )
        show_logs_btn.click(
            fn=self._get_system_logs,
            inputs=[log_level],
            outputs=[logs_display]
        )
    
    def _create_analytics_tab(self):
        """Create analytics and reports tab."""
        
        gr.Markdown("### Analityka i raporty")
        
        with gr.Row():
            with gr.Column():
                # Report type selection
                report_type = gr.Radio(
                    choices=[
                        "Analiza emaili",
                        "Statystyki ofert",
                        "Wydajność sprzętu",
                        "Trendy rynkowe"
                    ],
                    value="Analiza emaili",
                    label="Typ raportu"
                )
                
                # Date range
                date_from = gr.Textbox(
                    label="Data od (YYYY-MM-DD)",
                    value="2024-01-01"
                )
                date_to = gr.Textbox(
                    label="Data do (YYYY-MM-DD)",
                    value=datetime.now().strftime("%Y-%m-%d")
                )
                
                generate_report_btn = gr.Button("📊 Generuj raport", variant="primary")
            
            with gr.Column():
                # Report display
                report_chart = gr.Plot(label="Wykres")
                report_data = gr.Dataframe(label="Dane")
                
                # Export options
                export_csv_btn = gr.Button("📄 Eksportuj CSV")
                export_pdf_btn = gr.Button("📋 Eksportuj PDF")
        
        # Event handlers
        generate_report_btn.click(
            fn=self._generate_report,
            inputs=[report_type, date_from, date_to],
            outputs=[report_chart, report_data]
        )
    
    # Implementation methods
    def _get_system_status_html(self) -> str:
        """Get system status as HTML."""
        try:
            # Check system components
            db_status = "✅ Połączono" if db_manager.is_connected() else "❌ Rozłączono"
            krabulon_status = "✅ Aktywny" if self.krabulon_orchestrator else "❌ Nieaktywny"
            
            return f"""
            <div class="status-success">
                <h4>Status systemu: Operacyjny</h4>
                <ul>
                    <li>Baza danych: {db_status}</li>
                    <li>Krabulon: {krabulon_status}</li>
                    <li>Ostatnia aktualizacja: {datetime.now().strftime('%H:%M:%S')}</li>
                </ul>
            </div>
            """
        except Exception as e:
            return f"""
            <div class="status-error">
                <h4>Status systemu: Błąd</h4>
                <p>Błąd: {str(e)}</p>
            </div>
            """
    
    def _refresh_system_status(self) -> str:
        """Refresh system status."""
        return self._get_system_status_html()
    
    def _analyze_email(
        self,
        email_content: str,
        framework: str,
        include_sentiment: bool,
        include_entities: bool,
        include_intent: bool
    ) -> Tuple[Dict, str, str]:
        """Analyze email content."""
        try:
            if not email_content.strip():
                return {}, "Brak treści do analizy", "Wprowadź treść emaila"
            
            # Run analysis based on selected framework
            framework_map = {
                "LangGraph": "langgraph",
                "CrewAI": "crewai",
                "OpenAI Swarm": "swarm"
            }
            
            result = self.hvac_system.run_framework(framework_map[framework])
            
            # Extract insights
            analysis_result = result.get("result", {})
            
            # Generate customer insights
            insights = self._extract_customer_insights(analysis_result)
            
            # Generate recommendations
            recommendations = self._extract_recommendations(analysis_result)
            
            return analysis_result, insights, recommendations
            
        except Exception as e:
            logger.error(f"Email analysis failed: {e}")
            return {"error": str(e)}, "Błąd analizy", "Spróbuj ponownie"
    
    def _extract_customer_insights(self, analysis_result: Dict) -> str:
        """Extract customer insights from analysis."""
        # Implementation would extract key insights
        return "Klient wykazuje zainteresowanie systemem klimatyzacji dla domu jednorodzinnego."
    
    def _extract_recommendations(self, analysis_result: Dict) -> str:
        """Extract recommendations from analysis."""
        # Implementation would generate recommendations
        return "Zaproponuj system split LG z funkcją WiFi."


def launch_interface():
    """Launch the Gradio interface."""
    interface_manager = HVACGradioInterface()
    interface = interface_manager.create_interface()
    
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True,
        show_error=True
    )


if __name__ == "__main__":
    launch_interface()
